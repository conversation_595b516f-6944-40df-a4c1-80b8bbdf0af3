import fs from "fs";
import path from "path";
import { pipeline } from "stream/promises";
import { createWriteStream } from "fs";
import { tmpdir } from "os";
import OpenAI from "openai";
import { combineWordsToSentences } from "@/lib/utils-transcript";

// Initialize OpenAI client with DeepInfra configuration
const openai = new OpenAI({
	baseURL: "https://api.deepinfra.com/v1/openai",
	apiKey: process.env.DEEPINFRA_API_KEY,
});

/**
 * Download file from URL to a temporary location using streams
 * @param fileUrl - URL of the file to download
 * @returns Promise<string> - Path to the downloaded temporary file
 */
async function downloadFileToTemp(fileUrl: string): Promise<string> {
	const response = await fetch(fileUrl, {
		headers: {
			"Cache-Control": "no-cache",
		},
	});
	if (!response.ok) {
		throw new Error(`Failed to fetch audio file: ${response.status} ${response.statusText}`);
	}

	if (!response.body) {
		throw new Error("Response body is null");
	}

	// Create temporary file path
	const tempFileName = `audio_${Date.now()}_${Math.random().toString(36).substring(7)}`;
	const tempFilePath = path.join(tmpdir(), tempFileName);

	// Stream the response to a temporary file
	const fileStream = createWriteStream(tempFilePath);

	// Convert Web ReadableStream to Node.js Readable stream
	const { Readable } = await import("stream");

	const nodeStream = Readable.fromWeb(response.body as any);

	// Use pipeline to handle the streaming with proper error handling
	await pipeline(nodeStream, fileStream);

	return tempFilePath;
}

/**
 * Clean up temporary file
 * @param filePath - Path to the temporary file to delete
 */
async function cleanupTempFile(filePath: string): Promise<void> {
	try {
		await fs.promises.unlink(filePath);
	} catch (error) {
		console.warn(`Failed to cleanup temporary file ${filePath}:`, error);
	}
}

/**
 * Transcribe audio from a URL using OpenAI Whisper via DeepInfra
 * @param fileUrl - URL of the audio file to transcribe
 * @param languageCode - Language code for transcription (e.g., 'en', 'zh', 'es')
 * @returns Promise<string> - The transcribed text
 */
export async function transcribeFromDeepinfra(
	fileUrl: string, // eg. https://example.com/file.mp3
	languageCode: string,
): Promise<SentenceData[]> {
	let tempFilePath: string | null = null;

	try {
		// Check if it's a URL or local file path
		const isUrl = fileUrl.startsWith("http://") || fileUrl.startsWith("https://");

		let audioFilePath: string;

		// if (isUrl) {
		// 	// Download file to temporary location using streams
		// 	console.log(`Downloading audio file from URL: ${fileUrl}`);
		// 	tempFilePath = await downloadFileToTemp(fileUrl);
		// 	audioFilePath = tempFilePath;
		// } else {
		// 	// Use local file path directly
		// 	audioFilePath = fileUrl;

		// 	// Verify file exists
		// 	if (!fs.existsSync(audioFilePath)) {
		// 		throw new Error(`Local audio file not found: ${audioFilePath}`);
		// 	}
		// }

		// console.log(`Transcribing audio file: ${audioFilePath}`);

		// // Create a read stream for the audio file (this doesn't load the entire file into memory)
		// const audioStream = fs.createReadStream(audioFilePath);

		// // Perform transcription using OpenAI Whisper
		// const transcription = await openai.audio.transcriptions.create({
		// 	file: audioStream,
		// 	model: "openai/whisper-large-v3-turbo",
		// 	language: languageCode,
		// 	response_format: "verbose_json",
		// 	timestamp_granularities: ["segment", "word"],
		// });

		const transcription = {
			text: "If you're wondering why this is cut off, it's because the only thing I could stand my phone on was like this, it's kind of the indented suitcase, so it's kind of, this is just there for reasons. I want to let you know I haven't been uploading videos for about a week now. Originally, that was because I was out of town working on movie stuff, TM, but I found out earlier this week, just a few days ago, that my uncle passed away. So I was not prepared for that. Obviously, no one is prepared for that, but I was literally not prepared because I wasn't.  home i wasn't home you know uh so i had to take care of some things first and now i'm going to be going uh to cincinnati to help make arrangements and help you know the rest of my family with that um and i know i've gone for extended periods of time when i was working on movie stuff that i just basically haven't said anything because i was so hyper focused but this is one of those times where i really wanted to let you guys know what was going on um you know i know there's the plan and stuff like that but none of that really matters when there's something  important to take care of. So I'm going to take care of that. And I'm not exactly sure when I'm going to be back. Um, it just depends on how much time I need to be out there. And obviously I know a lot of people are going to say it, but obviously I'm not going to focus on trying to get back to making content or working on other stuff. So these things take precedent and these things need to be, you know, taken care of and, you know, family's so important. So I would never want anyone to.  you know, prioritize work over that. So I'm definitely not going to do the same either. And, and I wouldn't in any other circumstance. Um, so just wanted to let you guys know that way you don't think that I just disappeared working on movie stuff or whatever other inning project that I was, you know, caught off guard with and started to obsess over. So, um, yeah, just wanted to update you on that. Don't worry about anything else. Uh, you know, it'll be a while before I get back. I'm going to take exactly as long as I need.  Um, and it could be, you know, it could be next week. It could be another week. It could be a couple of weeks. Um, but you know, I'm just going to be out there as long as I need to be, uh, to help out. And you know, I'll come back when the time's right. Just want to let you know. All right. So thanks. Love you. Bye.",
			task: "transcribe",
			language: "en",
			duration: 126.966,
			words: [
				{ word: " 我 If", start: 0, end: 0.1599999964237213 },
				{ word: " you're", start: 0.1599999964237213, end: 0.30000001192092896 },
				{ word: " wondering", start: 0.30000001192092896, end: 0.5600000023841858 },
				{ word: " why", start: 0.5600000023841858, end: 0.7799999713897705 },
				{ word: " this", start: 0.7799999713897705, end: 0.9800000190734863 },
				{ word: " is", start: 0.9800000190734863, end: 1.1200000047683716 },
				{ word: " cut", start: 1.1200000047683716, end: 1.2200000286102295 },
				{ word: " off,", start: 1.2200000286102295, end: 1.399999976158142 },
				{ word: " it's", start: 1.5199999809265137, end: 1.5800000429153442 },
				{ word: " because", start: 1.5800000429153442, end: 1.7599999904632568 },
				{ word: " the", start: 1.7599999904632568, end: 1.9199999570846558 },
				{ word: " only", start: 1.9199999570846558, end: 2.0799999237060547 },
				{ word: " thing", start: 2.0799999237060547, end: 2.2200000286102295 },
				{ word: " I", start: 2.2200000286102295, end: 2.359999895095825 },
				{ word: " could", start: 2.359999895095825, end: 2.5 },
				{ word: " stand", start: 2.5, end: 2.7799999713897705 },
				{ word: " my", start: 2.7799999713897705, end: 2.9800000190734863 },
				{ word: " phone", start: 2.9800000190734863, end: 3.2200000286102295 },
				{ word: " on", start: 3.2200000286102295, end: 3.4200000762939453 },
				{ word: " was", start: 3.4200000762939453, end: 3.619999885559082 },
				{ word: " like", start: 3.619999885559082, end: 3.759999990463257 },
				{ word: " this,", start: 3.759999990463257, end: 4.019999980926514 },
				{ word: " it's", start: 4.119999885559082, end: 4.739999771118164 },
				{ word: " kind", start: 4.739999771118164, end: 4.860000133514404 },
				{ word: " of", start: 4.860000133514404, end: 4.940000057220459 },
				{ word: " the", start: 4.940000057220459, end: 5.059999942779541 },
				{ word: " indented", start: 5.059999942779541, end: 5.5 },
				{ word: " suitcase,", start: 5.5, end: 5.980000019073486 },
				{ word: " so", start: 6.21999979019165, end: 6.28000020980835 },
				{ word: " it's", start: 6.28000020980835, end: 6.440000057220459 },
				{ word: " kind", start: 6.440000057220459, end: 6.599999904632568 },
				{ word: " of,", start: 6.599999904632568, end: 6.659999847412109 },
				{ word: " this", start: 6.760000228881836, end: 6.880000114440918 },
				{ word: " is", start: 6.880000114440918, end: 7.619999885559082 },
				{ word: " just", start: 7.619999885559082, end: 7.880000114440918 },
				{ word: " there", start: 7.880000114440918, end: 8.420000076293945 },
				{ word: " for", start: 8.420000076293945, end: 8.779999732971191 },
				{ word: " reasons.", start: 8.779999732971191, end: 9.579999923706055 },
				{ word: " I", start: 10.380000114440918, end: 10.779999732971191 },
				{ word: " want", start: 10.779999732971191, end: 10.899999618530273 },
				{ word: " to", start: 10.899999618530273, end: 10.960000038146973 },
				{ word: " let", start: 10.960000038146973, end: 11.100000381469727 },
				{ word: " you", start: 11.100000381469727, end: 11.199999809265137 },
				{ word: " know", start: 11.199999809265137, end: 11.380000114440918 },
				{ word: " I", start: 11.380000114440918, end: 11.619999885559082 },
				{ word: " haven't", start: 11.619999885559082, end: 11.779999732971191 },
				{ word: " been", start: 11.779999732971191, end: 11.899999618530273 },
				{ word: " uploading", start: 11.899999618530273, end: 12.199999809265137 },
				{ word: " videos", start: 12.199999809265137, end: 12.5600004196167 },
				{ word: " for", start: 12.5600004196167, end: 12.699999809265137 },
				{ word: " about", start: 12.699999809265137, end: 12.899999618530273 },
				{ word: " a", start: 12.899999618530273, end: 13.020000457763672 },
				{ word: " week", start: 13.020000457763672, end: 13.260000228881836 },
				{ word: " now.", start: 13.260000228881836, end: 13.5600004196167 },
				{ word: " Originally,", start: 14.140000343322754, end: 14.539999961853027 },
				{ word: " that", start: 14.539999961853027, end: 14.819999694824219 },
				{ word: " was", start: 14.819999694824219, end: 14.9399995803833 },
				{ word: " because", start: 14.9399995803833, end: 15.180000305175781 },
				{ word: " I", start: 15.180000305175781, end: 15.380000114440918 },
				{ word: " was", start: 15.380000114440918, end: 15.579999923706055 },
				{ word: " out", start: 15.579999923706055, end: 16.219999313354492 },
				{ word: " of", start: 16.219999313354492, end: 16.34000015258789 },
				{ word: " town", start: 16.34000015258789, end: 16.639999389648438 },
				{ word: " working", start: 16.639999389648438, end: 17.059999465942383 },
				{ word: " on", start: 17.059999465942383, end: 17.260000228881836 },
				{ word: " movie", start: 17.260000228881836, end: 17.559999465942383 },
				{ word: " stuff,", start: 17.559999465942383, end: 17.860000610351562 },
				{ word: " TM,", start: 18.079999923706055, end: 18.420000076293945 },
				{ word: " but", start: 18.799999237060547, end: 19.239999771118164 },
				{ word: " I", start: 19.239999771118164, end: 19.34000015258789 },
				{ word: " found", start: 19.34000015258789, end: 19.579999923706055 },
				{ word: " out", start: 19.579999923706055, end: 19.739999771118164 },
				{ word: " earlier", start: 19.739999771118164, end: 20.020000457763672 },
				{ word: " this", start: 20.020000457763672, end: 20.280000686645508 },
				{ word: " week,", start: 20.280000686645508, end: 20.520000457763672 },
				{ word: " just", start: 20.68000030517578, end: 20.799999237060547 },
				{ word: " a", start: 20.799999237060547, end: 20.920000076293945 },
				{ word: " few", start: 20.920000076293945, end: 21.020000457763672 },
				{ word: " days", start: 21.020000457763672, end: 21.260000228881836 },
				{ word: " ago,", start: 21.260000228881836, end: 21.520000457763672 },
				{ word: " that", start: 21.940000534057617, end: 22.34000015258789 },
				{ word: " my", start: 22.34000015258789, end: 22.479999542236328 },
				{ word: " uncle", start: 22.479999542236328, end: 22.739999771118164 },
				{ word: " passed", start: 22.739999771118164, end: 23.079999923706055 },
				{ word: " away.", start: 23.079999923706055, end: 23.31999969482422 },
				{ word: " So", start: 23.5, end: 23.860000610351562 },
				{ word: " I", start: 23.860000610351562, end: 24.579999923706055 },
				{ word: " was", start: 24.579999923706055, end: 24.719999313354492 },
				{ word: " not", start: 24.719999313354492, end: 24.84000015258789 },
				{ word: " prepared", start: 24.84000015258789, end: 25.3799991607666 },
				{ word: " for", start: 25.3799991607666, end: 25.65999984741211 },
				{ word: " that.", start: 25.65999984741211, end: 25.860000610351562 },
				{ word: " Obviously,", start: 25.979999542236328, end: 26.299999237060547 },
				{ word: " no", start: 26.299999237060547, end: 26.459999084472656 },
				{ word: " one", start: 26.459999084472656, end: 26.579999923706055 },
				{ word: " is", start: 26.579999923706055, end: 26.719999313354492 },
				{ word: " prepared", start: 26.719999313354492, end: 26.979999542236328 },
				{ word: " for", start: 26.979999542236328, end: 27.139999389648438 },
				{ word: " that,", start: 27.139999389648438, end: 27.239999771118164 },
				{ word: " but", start: 27.31999969482422, end: 27.3799991607666 },
				{ word: " I", start: 27.3799991607666, end: 27.440000534057617 },
				{ word: " was", start: 27.440000534057617, end: 27.579999923706055 },
				{ word: " literally", start: 27.579999923706055, end: 27.940000534057617 },
				{ word: " not", start: 27.940000534057617, end: 28.260000228881836 },
				{ word: " prepared", start: 28.260000228881836, end: 28.540000915527344 },
				{ word: " because", start: 28.540000915527344, end: 28.780000686645508 },
				{ word: " I", start: 28.780000686645508, end: 29.15999984741211 },
				{ word: " wasn't.", start: 29.15999984741211, end: 29.5 },
				{ word: " home", start: 29.8700008392334, end: 30.229999542236328 },
				{ word: " i", start: 30.229999542236328, end: 30.389999389648438 },
				{ word: " wasn't", start: 30.389999389648438, end: 30.729999542236328 },
				{ word: " home", start: 30.729999542236328, end: 31.389999389648438 },
				{ word: " you", start: 31.389999389648438, end: 31.829999923706055 },
				{ word: " know", start: 31.829999923706055, end: 31.950000762939453 },
				{ word: " uh", start: 31.950000762939453, end: 32.25 },
				{ word: " so", start: 32.25, end: 32.66999816894531 },
				{ word: " i", start: 32.66999816894531, end: 32.790000915527344 },
				{ word: " had", start: 32.790000915527344, end: 32.93000030517578 },
				{ word: " to", start: 32.93000030517578, end: 33.029998779296875 },
				{ word: " take", start: 33.029998779296875, end: 33.189998626708984 },
				{ word: " care", start: 33.189998626708984, end: 33.349998474121094 },
				{ word: " of", start: 33.349998474121094, end: 33.45000076293945 },
				{ word: " some", start: 33.45000076293945, end: 33.61000061035156 },
				{ word: " things", start: 33.61000061035156, end: 33.83000183105469 },
				{ word: " first", start: 33.83000183105469, end: 34.189998626708984 },
				{ word: " and", start: 34.189998626708984, end: 34.83000183105469 },
				{ word: " now", start: 34.83000183105469, end: 35.04999923706055 },
				{ word: " i'm", start: 35.04999923706055, end: 35.22999954223633 },
				{ word: " going", start: 35.22999954223633, end: 35.349998474121094 },
				{ word: " to", start: 35.349998474121094, end: 35.43000030517578 },
				{ word: " be", start: 35.43000030517578, end: 35.529998779296875 },
				{ word: " going", start: 35.529998779296875, end: 35.869998931884766 },
				{ word: " uh", start: 35.869998931884766, end: 36.45000076293945 },
				{ word: " to", start: 36.45000076293945, end: 36.689998626708984 },
				{ word: " cincinnati", start: 36.689998626708984, end: 37.150001525878906 },
				{ word: " to", start: 37.150001525878906, end: 37.33000183105469 },
				{ word: " help", start: 37.33000183105469, end: 37.5099983215332 },
				{ word: " make", start: 37.5099983215332, end: 37.849998474121094 },
				{ word: " arrangements", start: 37.849998474121094, end: 38.689998626708984 },
				{ word: " and", start: 38.689998626708984, end: 38.9900016784668 },
				{ word: " help", start: 38.9900016784668, end: 39.25 },
				{ word: " you", start: 39.25, end: 39.43000030517578 },
				{ word: " know", start: 39.43000030517578, end: 39.5099983215332 },
				{ word: " the", start: 39.5099983215332, end: 39.709999084472656 },
				{ word: " rest", start: 39.709999084472656, end: 39.849998474121094 },
				{ word: " of", start: 39.849998474121094, end: 39.970001220703125 },
				{ word: " my", start: 39.970001220703125, end: 40.09000015258789 },
				{ word: " family", start: 40.09000015258789, end: 40.369998931884766 },
				{ word: " with", start: 40.369998931884766, end: 40.529998779296875 },
				{ word: " that", start: 40.529998779296875, end: 40.709999084472656 },
				{ word: " um", start: 40.709999084472656, end: 41.369998931884766 },
				{ word: " and", start: 41.369998931884766, end: 41.75 },
				{ word: " i", start: 41.75, end: 41.83000183105469 },
				{ word: " know", start: 41.83000183105469, end: 41.93000030517578 },
				{ word: " i've", start: 41.93000030517578, end: 42.029998779296875 },
				{ word: " gone", start: 42.029998779296875, end: 42.150001525878906 },
				{ word: " for", start: 42.150001525878906, end: 42.310001373291016 },
				{ word: " extended", start: 42.310001373291016, end: 42.630001068115234 },
				{ word: " periods", start: 42.630001068115234, end: 42.93000030517578 },
				{ word: " of", start: 42.93000030517578, end: 43.04999923706055 },
				{ word: " time", start: 43.04999923706055, end: 43.22999954223633 },
				{ word: " when", start: 43.22999954223633, end: 43.40999984741211 },
				{ word: " i", start: 43.40999984741211, end: 43.470001220703125 },
				{ word: " was", start: 43.470001220703125, end: 43.59000015258789 },
				{ word: " working", start: 43.59000015258789, end: 43.83000183105469 },
				{ word: " on", start: 43.83000183105469, end: 44.04999923706055 },
				{ word: " movie", start: 44.04999923706055, end: 44.38999938964844 },
				{ word: " stuff", start: 44.38999938964844, end: 44.630001068115234 },
				{ word: " that", start: 44.630001068115234, end: 44.83000183105469 },
				{ word: " i", start: 44.83000183105469, end: 44.90999984741211 },
				{ word: " just", start: 44.90999984741211, end: 45.06999969482422 },
				{ word: " basically", start: 45.06999969482422, end: 45.4900016784668 },
				{ word: " haven't", start: 45.4900016784668, end: 46.0099983215332 },
				{ word: " said", start: 46.0099983215332, end: 46.16999816894531 },
				{ word: " anything", start: 46.16999816894531, end: 46.45000076293945 },
				{ word: " because", start: 46.45000076293945, end: 46.709999084472656 },
				{ word: " i", start: 46.709999084472656, end: 46.83000183105469 },
				{ word: " was", start: 46.83000183105469, end: 46.93000030517578 },
				{ word: " so", start: 46.93000030517578, end: 47.04999923706055 },
				{ word: " hyper", start: 47.04999923706055, end: 47.33000183105469 },
				{ word: " focused", start: 47.33000183105469, end: 47.630001068115234 },
				{ word: " but", start: 47.630001068115234, end: 47.83000183105469 },
				{ word: " this", start: 47.83000183105469, end: 47.9900016784668 },
				{ word: " is", start: 47.9900016784668, end: 48.06999969482422 },
				{ word: " one", start: 48.06999969482422, end: 48.189998626708984 },
				{ word: " of", start: 48.189998626708984, end: 48.290000915527344 },
				{ word: " those", start: 48.290000915527344, end: 48.40999984741211 },
				{ word: " times", start: 48.40999984741211, end: 48.61000061035156 },
				{ word: " where", start: 48.61000061035156, end: 48.810001373291016 },
				{ word: " i", start: 48.810001373291016, end: 48.90999984741211 },
				{ word: " really", start: 48.90999984741211, end: 49.130001068115234 },
				{ word: " wanted", start: 49.130001068115234, end: 49.38999938964844 },
				{ word: " to", start: 49.38999938964844, end: 49.5099983215332 },
				{ word: " let", start: 49.5099983215332, end: 49.56999969482422 },
				{ word: " you", start: 49.56999969482422, end: 49.689998626708984 },
				{ word: " guys", start: 49.689998626708984, end: 49.869998931884766 },
				{ word: " know", start: 49.869998931884766, end: 50.06999969482422 },
				{ word: " what", start: 50.06999969482422, end: 50.25 },
				{ word: " was", start: 50.25, end: 50.369998931884766 },
				{ word: " going", start: 50.369998931884766, end: 50.54999923706055 },
				{ word: " on", start: 50.54999923706055, end: 50.849998474121094 },
				{ word: " um", start: 50.849998474121094, end: 51.66999816894531 },
				{ word: " you", start: 51.66999816894531, end: 52.189998626708984 },
				{ word: " know", start: 52.189998626708984, end: 52.290000915527344 },
				{ word: " i", start: 52.290000915527344, end: 52.43000030517578 },
				{ word: " know", start: 52.43000030517578, end: 52.54999923706055 },
				{ word: " there's", start: 52.54999923706055, end: 52.75 },
				{ word: " the", start: 52.75, end: 53.310001373291016 },
				{ word: " plan", start: 53.310001373291016, end: 53.66999816894531 },
				{ word: " and", start: 53.66999816894531, end: 53.90999984741211 },
				{ word: " stuff", start: 53.90999984741211, end: 54.09000015258789 },
				{ word: " like", start: 54.09000015258789, end: 54.22999954223633 },
				{ word: " that", start: 54.22999954223633, end: 54.349998474121094 },
				{ word: " but", start: 54.349998474121094, end: 54.5099983215332 },
				{ word: " none", start: 54.5099983215332, end: 54.630001068115234 },
				{ word: " of", start: 54.630001068115234, end: 54.77000045776367 },
				{ word: " that", start: 54.77000045776367, end: 54.869998931884766 },
				{ word: " really", start: 54.869998931884766, end: 55.09000015258789 },
				{ word: " matters", start: 55.09000015258789, end: 55.54999923706055 },
				{ word: " when", start: 55.54999923706055, end: 56.209999084472656 },
				{ word: " there's", start: 56.209999084472656, end: 56.529998779296875 },
				{ word: " something", start: 56.529998779296875, end: 56.849998474121094 },
				{ word: " important", start: 57.59000015258789, end: 57.95000076293945 },
				{ word: " to", start: 57.95000076293945, end: 58.11000061035156 },
				{ word: " take", start: 58.11000061035156, end: 58.22999954223633 },
				{ word: " care", start: 58.22999954223633, end: 58.43000030517578 },
				{ word: " of.", start: 58.43000030517578, end: 58.59000015258789 },
				{ word: " So", start: 58.689998626708984, end: 58.810001373291016 },
				{ word: " I'm", start: 58.810001373291016, end: 59.150001525878906 },
				{ word: " going", start: 59.150001525878906, end: 59.290000915527344 },
				{ word: " to", start: 59.290000915527344, end: 59.45000076293945 },
				{ word: " take", start: 59.45000076293945, end: 59.61000061035156 },
				{ word: " care", start: 59.61000061035156, end: 59.810001373291016 },
				{ word: " of", start: 59.810001373291016, end: 59.93000030517578 },
				{ word: " that.", start: 59.93000030517578, end: 60.150001525878906 },
				{ word: " And", start: 60.27000045776367, end: 60.38999938964844 },
				{ word: " I'm", start: 60.38999938964844, end: 60.5099983215332 },
				{ word: " not", start: 60.5099983215332, end: 60.650001525878906 },
				{ word: " exactly", start: 60.650001525878906, end: 60.9900016784668 },
				{ word: " sure", start: 60.9900016784668, end: 61.209999084472656 },
				{ word: " when", start: 61.209999084472656, end: 61.5099983215332 },
				{ word: " I'm", start: 61.5099983215332, end: 61.66999816894531 },
				{ word: " going", start: 61.66999816894531, end: 61.75 },
				{ word: " to", start: 61.75, end: 61.810001373291016 },
				{ word: " be", start: 61.810001373291016, end: 61.88999938964844 },
				{ word: " back.", start: 61.88999938964844, end: 62.209999084472656 },
				{ word: " Um,", start: 62.529998779296875, end: 62.88999938964844 },
				{ word: " it", start: 63.43000030517578, end: 63.61000061035156 },
				{ word: " just", start: 63.61000061035156, end: 63.75 },
				{ word: " depends", start: 63.75, end: 64.02999877929688 },
				{ word: " on", start: 64.02999877929688, end: 64.20999908447266 },
				{ word: " how", start: 64.20999908447266, end: 64.30999755859375 },
				{ word: " much", start: 64.30999755859375, end: 64.48999786376953 },
				{ word: " time", start: 64.48999786376953, end: 64.66999816894531 },
				{ word: " I", start: 64.66999816894531, end: 64.80999755859375 },
				{ word: " need", start: 64.80999755859375, end: 64.93000030517578 },
				{ word: " to", start: 64.93000030517578, end: 65.01000213623047 },
				{ word: " be", start: 65.01000213623047, end: 65.12999725341797 },
				{ word: " out", start: 65.12999725341797, end: 65.33000183105469 },
				{ word: " there.", start: 65.33000183105469, end: 65.52999877929688 },
				{ word: " And", start: 65.61000061035156, end: 65.97000122070312 },
				{ word: " obviously", start: 65.97000122070312, end: 66.43000030517578 },
				{ word: " I", start: 66.43000030517578, end: 66.66999816894531 },
				{ word: " know", start: 66.66999816894531, end: 66.83000183105469 },
				{ word: " a", start: 66.83000183105469, end: 66.97000122070312 },
				{ word: " lot", start: 66.97000122070312, end: 67.05000305175781 },
				{ word: " of", start: 67.05000305175781, end: 67.12999725341797 },
				{ word: " people", start: 67.12999725341797, end: 67.29000091552734 },
				{ word: " are", start: 67.29000091552734, end: 67.38999938964844 },
				{ word: " going", start: 67.38999938964844, end: 67.44999694824219 },
				{ word: " to", start: 67.44999694824219, end: 67.51000213623047 },
				{ word: " say", start: 67.51000213623047, end: 67.6500015258789 },
				{ word: " it,", start: 67.6500015258789, end: 67.7699966430664 },
				{ word: " but", start: 67.79000091552734, end: 67.8499984741211 },
				{ word: " obviously", start: 67.8499984741211, end: 68.12999725341797 },
				{ word: " I'm", start: 68.12999725341797, end: 68.47000122070312 },
				{ word: " not", start: 68.47000122070312, end: 68.6500015258789 },
				{ word: " going", start: 68.6500015258789, end: 68.79000091552734 },
				{ word: " to", start: 68.79000091552734, end: 68.88999938964844 },
				{ word: " focus", start: 68.88999938964844, end: 69.16999816894531 },
				{ word: " on", start: 69.16999816894531, end: 69.48999786376953 },
				{ word: " trying", start: 69.48999786376953, end: 70.30999755859375 },
				{ word: " to", start: 70.30999755859375, end: 70.6500015258789 },
				{ word: " get", start: 70.6500015258789, end: 70.79000091552734 },
				{ word: " back", start: 70.79000091552734, end: 71.02999877929688 },
				{ word: " to", start: 71.02999877929688, end: 71.25 },
				{ word: " making", start: 71.25, end: 71.51000213623047 },
				{ word: " content", start: 71.51000213623047, end: 71.98999786376953 },
				{ word: " or", start: 71.98999786376953, end: 72.43000030517578 },
				{ word: " working", start: 72.43000030517578, end: 72.87000274658203 },
				{ word: " on", start: 72.87000274658203, end: 73.02999877929688 },
				{ word: " other", start: 73.02999877929688, end: 73.29000091552734 },
				{ word: " stuff.", start: 73.29000091552734, end: 73.52999877929688 },
				{ word: " So", start: 73.66999816894531, end: 73.7699966430664 },
				{ word: " these", start: 73.7699966430664, end: 74.43000030517578 },
				{ word: " things", start: 74.43000030517578, end: 74.75 },
				{ word: " take", start: 74.75, end: 75.11000061035156 },
				{ word: " precedent", start: 75.11000061035156, end: 75.62999725341797 },
				{ word: " and", start: 75.62999725341797, end: 75.97000122070312 },
				{ word: " these", start: 75.97000122070312, end: 76.2300033569336 },
				{ word: " things", start: 76.2300033569336, end: 76.48999786376953 },
				{ word: " need", start: 76.48999786376953, end: 76.7300033569336 },
				{ word: " to", start: 76.7300033569336, end: 76.8499984741211 },
				{ word: " be,", start: 76.8499984741211, end: 77.11000061035156 },
				{ word: " you", start: 77.19000244140625, end: 77.58999633789062 },
				{ word: " know,", start: 77.58999633789062, end: 77.6500015258789 },
				{ word: " taken", start: 77.79000091552734, end: 77.94999694824219 },
				{ word: " care", start: 77.94999694824219, end: 78.19000244140625 },
				{ word: " of", start: 78.19000244140625, end: 78.38999938964844 },
				{ word: " and,", start: 78.38999938964844, end: 78.98999786376953 },
				{ word: " you", start: 78.98999786376953, end: 79.3499984741211 },
				{ word: " know,", start: 79.3499984741211, end: 79.43000030517578 },
				{ word: " family's", start: 79.62999725341797, end: 80.08999633789062 },
				{ word: " so", start: 80.08999633789062, end: 80.83000183105469 },
				{ word: " important.", start: 80.83000183105469, end: 81.25 },
				{ word: " So", start: 81.25, end: 81.48999786376953 },
				{ word: " I", start: 81.48999786376953, end: 81.66999816894531 },
				{ word: " would", start: 81.66999816894531, end: 81.7699966430664 },
				{ word: " never", start: 81.7699966430664, end: 81.94999694824219 },
				{ word: " want", start: 81.94999694824219, end: 82.1500015258789 },
				{ word: " anyone", start: 82.1500015258789, end: 82.41000366210938 },
				{ word: " to.", start: 82.41000366210938, end: 82.7300033569336 },
				{ word: " you", start: 83.52999877929688, end: 83.75 },
				{ word: " know,", start: 83.75, end: 83.87000274658203 },
				{ word: " prioritize", start: 84.01000213623047, end: 84.30999755859375 },
				{ word: " work", start: 84.30999755859375, end: 84.7300033569336 },
				{ word: " over", start: 84.7300033569336, end: 84.93000030517578 },
				{ word: " that.", start: 84.93000030517578, end: 85.08999633789062 },
				{ word: " So", start: 85.08999633789062, end: 85.2699966430664 },
				{ word: " I'm", start: 85.2699966430664, end: 85.43000030517578 },
				{ word: " definitely", start: 85.43000030517578, end: 85.66999816894531 },
				{ word: " not", start: 85.66999816894531, end: 85.87000274658203 },
				{ word: " going", start: 85.87000274658203, end: 85.98999786376953 },
				{ word: " to", start: 85.98999786376953, end: 86.06999969482422 },
				{ word: " do", start: 86.06999969482422, end: 86.19000244140625 },
				{ word: " the", start: 86.19000244140625, end: 86.29000091552734 },
				{ word: " same", start: 86.29000091552734, end: 86.43000030517578 },
				{ word: " either.", start: 86.43000030517578, end: 86.69000244140625 },
				{ word: " And,", start: 87.01000213623047, end: 87.1500015258789 },
				{ word: " and", start: 87.1500015258789, end: 87.33000183105469 },
				{ word: " I", start: 87.33000183105469, end: 87.44999694824219 },
				{ word: " wouldn't", start: 87.44999694824219, end: 87.6500015258789 },
				{ word: " in", start: 87.6500015258789, end: 87.79000091552734 },
				{ word: " any", start: 87.79000091552734, end: 87.94999694824219 },
				{ word: " other", start: 87.94999694824219, end: 88.12999725341797 },
				{ word: " circumstance.", start: 88.12999725341797, end: 88.62999725341797 },
				{ word: " Um,", start: 88.98999786376953, end: 89.08999633789062 },
				{ word: " so", start: 89.37000274658203, end: 89.51000213623047 },
				{ word: " just", start: 89.51000213623047, end: 90.11000061035156 },
				{ word: " wanted", start: 90.11000061035156, end: 90.30999755859375 },
				{ word: " to", start: 90.30999755859375, end: 90.44999694824219 },
				{ word: " let", start: 90.44999694824219, end: 90.55000305175781 },
				{ word: " you", start: 90.55000305175781, end: 90.66999816894531 },
				{ word: " guys", start: 90.66999816894531, end: 90.87000274658203 },
				{ word: " know", start: 90.87000274658203, end: 91.2699966430664 },
				{ word: " that", start: 91.2699966430664, end: 91.7300033569336 },
				{ word: " way", start: 91.7300033569336, end: 91.88999938964844 },
				{ word: " you", start: 91.88999938964844, end: 92.01000213623047 },
				{ word: " don't", start: 92.01000213623047, end: 92.1500015258789 },
				{ word: " think", start: 92.1500015258789, end: 92.30999755859375 },
				{ word: " that", start: 92.30999755859375, end: 92.44999694824219 },
				{ word: " I", start: 92.44999694824219, end: 92.55000305175781 },
				{ word: " just", start: 92.55000305175781, end: 92.7300033569336 },
				{ word: " disappeared", start: 92.7300033569336, end: 93.16999816894531 },
				{ word: " working", start: 93.16999816894531, end: 93.44999694824219 },
				{ word: " on", start: 93.44999694824219, end: 93.61000061035156 },
				{ word: " movie", start: 93.61000061035156, end: 93.87000274658203 },
				{ word: " stuff", start: 93.87000274658203, end: 94.1500015258789 },
				{ word: " or", start: 94.1500015258789, end: 94.52999877929688 },
				{ word: " whatever", start: 94.52999877929688, end: 95.51000213623047 },
				{ word: " other", start: 95.51000213623047, end: 95.94999694824219 },
				{ word: " inning", start: 95.94999694824219, end: 96.25 },
				{ word: " project", start: 96.25, end: 96.6500015258789 },
				{ word: " that", start: 96.6500015258789, end: 96.87000274658203 },
				{ word: " I", start: 96.87000274658203, end: 96.97000122070312 },
				{ word: " was,", start: 96.97000122070312, end: 97.12999725341797 },
				{ word: " you", start: 97.29000091552734, end: 97.61000061035156 },
				{ word: " know,", start: 97.61000061035156, end: 97.70999908447266 },
				{ word: " caught", start: 98.01000213623047, end: 98.20999908447266 },
				{ word: " off", start: 98.20999908447266, end: 98.37000274658203 },
				{ word: " guard", start: 98.37000274658203, end: 98.56999969482422 },
				{ word: " with", start: 98.56999969482422, end: 98.7699966430664 },
				{ word: " and", start: 98.7699966430664, end: 98.91000366210938 },
				{ word: " started", start: 98.91000366210938, end: 99.16999816894531 },
				{ word: " to", start: 99.16999816894531, end: 99.30999755859375 },
				{ word: " obsess", start: 99.30999755859375, end: 99.55000305175781 },
				{ word: " over.", start: 99.55000305175781, end: 99.8499984741211 },
				{ word: " So,", start: 100.01000213623047, end: 100.11000061035156 },
				{ word: " um,", start: 100.8499984741211, end: 101.05000305175781 },
				{ word: " yeah,", start: 101.51000213623047, end: 101.6500015258789 },
				{ word: " just", start: 101.70999908447266, end: 101.83000183105469 },
				{ word: " wanted", start: 101.83000183105469, end: 102.01000213623047 },
				{ word: " to", start: 102.01000213623047, end: 102.1500015258789 },
				{ word: " update", start: 102.1500015258789, end: 102.37000274658203 },
				{ word: " you", start: 102.37000274658203, end: 102.51000213623047 },
				{ word: " on", start: 102.51000213623047, end: 102.6500015258789 },
				{ word: " that.", start: 102.6500015258789, end: 102.83000183105469 },
				{ word: " Don't", start: 102.98999786376953, end: 103.11000061035156 },
				{ word: " worry", start: 103.11000061035156, end: 103.20999908447266 },
				{ word: " about", start: 103.20999908447266, end: 103.37000274658203 },
				{ word: " anything", start: 103.37000274658203, end: 103.62999725341797 },
				{ word: " else.", start: 103.62999725341797, end: 103.94999694824219 },
				{ word: " Uh,", start: 104.19000244140625, end: 104.33000183105469 },
				{ word: " you", start: 104.79000091552734, end: 104.91000366210938 },
				{ word: " know,", start: 104.91000366210938, end: 104.98999786376953 },
				{ word: " it'll", start: 105.06999969482422, end: 105.20999908447266 },
				{ word: " be", start: 105.20999908447266, end: 105.33000183105469 },
				{ word: " a", start: 105.33000183105469, end: 105.48999786376953 },
				{ word: " while", start: 105.48999786376953, end: 105.66999816894531 },
				{ word: " before", start: 105.66999816894531, end: 105.93000030517578 },
				{ word: " I", start: 105.93000030517578, end: 106.06999969482422 },
				{ word: " get", start: 106.06999969482422, end: 106.20999908447266 },
				{ word: " back.", start: 106.20999908447266, end: 106.43000030517578 },
				{ word: " I'm", start: 106.55000305175781, end: 106.6500015258789 },
				{ word: " going", start: 106.6500015258789, end: 106.69000244140625 },
				{ word: " to", start: 106.69000244140625, end: 106.75 },
				{ word: " take", start: 106.75, end: 106.91000366210938 },
				{ word: " exactly", start: 106.91000366210938, end: 107.30999755859375 },
				{ word: " as", start: 107.30999755859375, end: 107.47000122070312 },
				{ word: " long", start: 107.47000122070312, end: 107.58999633789062 },
				{ word: " as", start: 107.58999633789062, end: 107.75 },
				{ word: " I", start: 107.75, end: 107.87000274658203 },
				{ word: " need.", start: 107.87000274658203, end: 108.1500015258789 },
				{ word: " Um,", start: 108.7300033569336, end: 109.05000305175781 },
				{ word: " and", start: 109.48999786376953, end: 109.7300033569336 },
				{ word: " it", start: 109.7300033569336, end: 109.8499984741211 },
				{ word: " could", start: 109.8499984741211, end: 109.97000122070312 },
				{ word: " be,", start: 109.97000122070312, end: 110.1500015258789 },
				{ word: " you", start: 110.2300033569336, end: 110.3499984741211 },
				{ word: " know,", start: 110.3499984741211, end: 110.38999938964844 },
				{ word: " it", start: 110.48999786376953, end: 110.55000305175781 },
				{ word: " could", start: 110.55000305175781, end: 110.66999816894531 },
				{ word: " be", start: 110.66999816894531, end: 110.83000183105469 },
				{ word: " next", start: 110.83000183105469, end: 111.2699966430664 },
				{ word: " week.", start: 111.2699966430664, end: 111.48999786376953 },
				{ word: " It", start: 111.61000061035156, end: 111.61000061035156 },
				{ word: " could", start: 111.61000061035156, end: 111.69000244140625 },
				{ word: " be", start: 111.69000244140625, end: 111.79000091552734 },
				{ word: " another", start: 111.79000091552734, end: 111.97000122070312 },
				{ word: " week.", start: 111.97000122070312, end: 112.2300033569336 },
				{ word: " It", start: 112.38999938964844, end: 112.38999938964844 },
				{ word: " could", start: 112.38999938964844, end: 112.44999694824219 },
				{ word: " be", start: 112.44999694824219, end: 112.55000305175781 },
				{ word: " a", start: 112.55000305175781, end: 112.6500015258789 },
				{ word: " couple", start: 112.6500015258789, end: 112.80999755859375 },
				{ word: " of", start: 112.80999755859375, end: 112.93000030517578 },
				{ word: " weeks.", start: 112.93000030517578, end: 113.1500015258789 },
				{ word: " Um,", start: 113.37000274658203, end: 113.55000305175781 },
				{ word: " but", start: 113.87000274658203, end: 114.05000305175781 },
				{ word: " you", start: 114.05000305175781, end: 114.25 },
				{ word: " know,", start: 114.25, end: 114.33000183105469 },
				{ word: " I'm", start: 114.41000366210938, end: 114.58999633789062 },
				{ word: " just", start: 114.58999633789062, end: 114.70999908447266 },
				{ word: " going", start: 114.70999908447266, end: 114.80999755859375 },
				{ word: " to", start: 114.80999755859375, end: 114.87000274658203 },
				{ word: " be", start: 114.87000274658203, end: 114.98999786376953 },
				{ word: " out", start: 114.98999786376953, end: 115.12999725341797 },
				{ word: " there", start: 115.12999725341797, end: 115.2699966430664 },
				{ word: " as", start: 115.2699966430664, end: 115.43000030517578 },
				{ word: " long", start: 115.43000030517578, end: 115.56999969482422 },
				{ word: " as", start: 115.56999969482422, end: 115.70999908447266 },
				{ word: " I", start: 115.70999908447266, end: 115.80999755859375 },
				{ word: " need", start: 115.80999755859375, end: 116.02999877929688 },
				{ word: " to", start: 116.02999877929688, end: 116.16999816894531 },
				{ word: " be,", start: 116.16999816894531, end: 116.48999786376953 },
				{ word: " uh,", start: 116.6500015258789, end: 117.05000305175781 },
				{ word: " to", start: 117.1500015258789, end: 117.25 },
				{ word: " help", start: 117.25, end: 117.47000122070312 },
				{ word: " out.", start: 117.47000122070312, end: 117.75 },
				{ word: " And", start: 118.01000213623047, end: 118.33000183105469 },
				{ word: " you", start: 118.33000183105469, end: 118.66999816894531 },
				{ word: " know,", start: 118.66999816894531, end: 118.7699966430664 },
				{ word: " I'll", start: 118.97000122070312, end: 119.19000244140625 },
				{ word: " come", start: 119.19000244140625, end: 119.3499984741211 },
				{ word: " back", start: 119.3499984741211, end: 119.51000213623047 },
				{ word: " when", start: 119.51000213623047, end: 119.69000244140625 },
				{ word: " the", start: 119.69000244140625, end: 119.80999755859375 },
				{ word: " time's", start: 119.80999755859375, end: 120.06999969482422 },
				{ word: " right.", start: 120.06999969482422, end: 120.2300033569336 },
				{ word: " Just", start: 120.69000244140625, end: 121.01000213623047 },
				{ word: " want", start: 121.01000213623047, end: 121.16999816894531 },
				{ word: " to", start: 121.16999816894531, end: 121.20999908447266 },
				{ word: " let", start: 121.20999908447266, end: 121.33000183105469 },
				{ word: " you", start: 121.33000183105469, end: 121.41000366210938 },
				{ word: " know.", start: 121.41000366210938, end: 121.58999633789062 },
				{ word: " All", start: 121.93000030517578, end: 121.98999786376953 },
				{ word: " right.", start: 121.98999786376953, end: 122.11000061035156 },
				{ word: " So", start: 122.29000091552734, end: 122.47000122070312 },
				{ word: " thanks.", start: 122.47000122070312, end: 123.02999877929688 },
				{ word: " Love", start: 124.25, end: 124.56999969482422 },
				{ word: " you.", start: 124.56999969482422, end: 125.19000244140625 },
				{ word: " Bye.", start: 125.29000091552734, end: 125.41000366210938 },
			],
		};

		// console.log("Transcription completed successfully");

		// console.log("Transcription result:", JSON.stringify(transcription, null, 2));

		const sentences: SentenceData[] = combineWordsToSentences(transcription.words);
		return sentences;
	} catch (error: any) {
		console.error("Transcription error:", error);

		// Handle specific error types
		if (error.message?.includes("fetch") || error.message?.includes("download")) {
			throw new Error(`Failed to download audio file from URL: ${error.message}`);
		}

		if (error.message?.includes("not found")) {
			throw new Error(`Audio file not found: ${error.message}`);
		}

		if (error.code === "ENOENT") {
			throw new Error(`Audio file not accessible: ${fileUrl}`);
		}

		if (error.status === 401) {
			throw new Error("Authentication failed: Invalid DeepInfra API key");
		}

		if (error.status === 429) {
			throw new Error("Rate limit exceeded: Too many requests to DeepInfra API");
		}

		// Generic error
		throw new Error(`Transcription error: ${error.message || "Unknown error"}`);
	} finally {
		// Clean up temporary file if it was created
		if (tempFilePath) {
			await cleanupTempFile(tempFilePath);
		}
	}
}
