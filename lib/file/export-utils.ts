"use client";

import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import { Document, Packer, Paragraph, TextRun } from "docx";

/**
 * Format time in seconds to MM:SS or HH:MM:SS format
 */
function formatTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);

	if (hours > 0) {
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
	}
	return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
}

/**
 * Export sentences as plain text (.txt)
 */
export function exportAsTxt(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.txt`);
}

/**
 * Export sentences as Markdown (.md)
 */
export function exportAsMarkdown(sentences: SentenceData[], filename: string = "transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");
	const blob = new Blob([content], { type: "text/markdown;charset=utf-8" });
	saveAs(blob, `${filename}.md`);
}

/**
 * Export sentences as SRT subtitle format (.srt)
 */
export function exportAsSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

/**
 * Format time for SRT format (HH:MM:SS,mmm)
 */
function formatSrtTime(seconds: number): string {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = Math.floor(seconds % 60);
	const milliseconds = Math.floor((seconds % 1) * 1000);

	return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")},${milliseconds.toString().padStart(3, "0")}`;
}

/**
 * Export sentences as PDF (.pdf)
 */
export function exportAsPdf(sentences: SentenceData[], filename: string = "transcript") {
	const doc = new jsPDF();
	const pageWidth = doc.internal.pageSize.getWidth();
	const pageHeight = doc.internal.pageSize.getHeight();
	const margin = 20;
	const maxWidth = pageWidth - 2 * margin;

	// Add title
	doc.setFontSize(16);
	doc.setFont("helvetica", "bold");
	doc.text("Transcript", margin, 30);

	// Add content
	doc.setFontSize(12);
	doc.setFont("helvetica", "normal");

	const content = sentences.map((sentence) => sentence.text).join(" ");
	const lines = doc.splitTextToSize(content, maxWidth);

	let yPosition = 50;
	const lineHeight = 7;

	lines.forEach((line: string) => {
		if (yPosition > pageHeight - margin) {
			doc.addPage();
			yPosition = margin;
		}
		// Check if the line contains CJK characters
		if (/[\u4e00-\u9fff\u3040-\u30ff\uac00-\ud7af]/.test(line)) {
			// Fallback: Currently using Helvetica which does not support CJK characters.
			// To properly display CJK text, a custom font must be integrated with jsPDF.
			// Recommended approach: Use a font like 'Noto Sans CJK' via a CDN or local file.
			// Example: doc.addFileToVFS('NotoSansCJK.ttf', fontData); doc.addFont('NotoSansCJK.ttf', 'NotoSansCJK', 'normal');
			// Then set: doc.setFont('NotoSansCJK', 'normal');
			doc.setFont("helvetica", "normal");
		}
		doc.text(line, margin, yPosition);
		yPosition += lineHeight;
	});

	doc.save(`${filename}.pdf`);
}

/**
 * Export sentences as Word document (.docx)
 */
export async function exportAsDocx(sentences: SentenceData[], filename: string = "Transcript") {
	const content = sentences.map((sentence) => sentence.text).join(" ");

	const doc = new Document({
		sections: [
			{
				properties: {},
				children: [
					new Paragraph({
						children: [
							new TextRun({
								text: filename,
								bold: true,
								size: 32,
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: "",
							}),
						],
					}),
					new Paragraph({
						children: [
							new TextRun({
								text: content,
								size: 24,
							}),
						],
					}),
				],
			},
		],
	});

	const buffer = (await Packer.toBuffer(doc)) as BlobPart;
	const blob = new Blob([buffer], {
		type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	});
	saveAs(blob, `${filename}.docx`);
}

/**
 * Export sentences with timestamps as detailed SRT
 */
export function exportAsDetailedSrt(sentences: SentenceData[], filename: string = "transcript") {
	const srtContent = sentences
		.map((sentence, index) => {
			const startTime = formatSrtTime(sentence.start);
			const endTime = formatSrtTime(sentence.end);

			return `${index + 1}\n${startTime} --> ${endTime}\n${sentence.text}\n`;
		})
		.join("\n");

	const blob = new Blob([srtContent], { type: "text/plain;charset=utf-8" });
	saveAs(blob, `${filename}.srt`);
}

export type ExportFormat = "txt" | "pdf" | "md" | "srt" | "docx";

/**
 * Main export function that handles all formats
 */
export async function exportTranscript(sentences: SentenceData[], format: ExportFormat, filename: string = "transcript") {
	switch (format) {
		case "txt":
			exportAsTxt(sentences, filename);
			break;
		case "pdf":
			exportAsPdf(sentences, filename);
			break;
		case "md":
			exportAsMarkdown(sentences, filename);
			break;
		case "srt":
			exportAsSrt(sentences, filename);
			break;
		case "docx":
			await exportAsDocx(sentences, filename);
			break;
		default:
			throw new Error(`Unsupported export format: ${format}`);
	}
}
